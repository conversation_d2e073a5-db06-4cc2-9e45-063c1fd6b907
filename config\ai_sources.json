{"sfw": {"name": "日常对话模型", "type": "openai", "endpoint": "https://api.openai.com/v1/chat/completions", "apiKey": "", "model": "gpt-3.5-turbo", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 2000}, "enabled": false, "description": "用于日常对话的标准模型"}, "nsfw": {"name": "内容创作模型", "type": "openai", "endpoint": "https://api.openai.com/v1/chat/completions", "apiKey": "", "model": "gpt-3.5-turbo", "parameters": {"temperature": 0.9, "top_p": 0.95, "max_tokens": 8192}, "enabled": false, "description": "处理创意内容的专用模型"}, "expert": {"name": "专家咨询模型", "type": "claude", "endpoint": "https://api.anthropic.com/v1/messages", "apiKey": "", "model": "claude-3-sonnet-20240229", "parameters": {"temperature": 0.5, "top_p": 0.9, "max_tokens": 4000}, "enabled": false, "description": "用于专业问题和技术咨询的高级模型"}, "detail-thinking": {"name": "详细思考模型", "type": "openai", "endpoint": "https://api.openai.com/v1/chat/completions", "apiKey": "", "model": "gpt-4", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}, "enabled": false, "description": "用于详细思考和复杂推理的高级模型"}, "web-browse": {"name": "网页浏览模型", "type": "openai", "endpoint": "https://api.openai.com/v1/chat/completions", "apiKey": "", "model": "gpt-3.5-turbo", "parameters": {"temperature": 0.6, "top_p": 0.8, "max_tokens": 4096}, "enabled": false, "description": "专为网页浏览和信息提取优化的模型"}, "logic": {"name": "快速逻辑模型", "type": "openai", "endpoint": "https://api.openai.com/v1/chat/completions", "apiKey": "", "model": "gpt-3.5-turbo", "parameters": {"temperature": 0.1, "top_p": 0.5, "max_tokens": 1000}, "enabled": false, "description": "快速响应的逻辑判断模型"}, "gemini": {"name": "Gemini AI模型", "type": "gemini", "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "apiKey": "", "model": "gemini-1.5-flash", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4000}, "enabled": false, "description": "Google Gemini AI模型，支持多模态输入"}, "local": {"name": "本地模型", "type": "local", "endpoint": "http://localhost:11434", "apiKey": "", "model": "llama2:7b", "parameters": {"temperature": 0.7, "top_p": 0.9, "repeat_penalty": 1.1}, "enabled": false, "description": "本地部署的开源模型"}}