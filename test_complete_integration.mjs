#!/usr/bin/env deno run --allow-all

/**
 * 完整的系统集成测试
 * 验证所有重构后的组件是否正常工作
 */

console.log('🧪 完整的系统集成测试...')

let allTestsPassed = true

try {
	// 1. 测试新配置系统
	console.log('\n📋 1. 测试新配置系统...')
	const { getConfigManager, initConfig } = await import('./config/config_manager.mjs')
	const configManager = await initConfig()
	
	const mainConfig = configManager.getMainConfig()
	const aiSources = configManager.getAISourcesConfig()
	const callingOrder = configManager.getCallingOrderConfig()
	
	console.log('✅ 新配置系统正常工作')
	console.log(`   主配置项: ${Object.keys(mainConfig).length} 个`)
	console.log(`   AI源配置: ${Object.keys(aiSources).length} 个`)
	console.log(`   调用顺序: ${Object.keys(callingOrder).length} 个`)

	// 2. 测试兼容接口
	console.log('\n🔄 2. 测试兼容接口...')
	const { GetData, SetData, getAISourceData } = await import('./config.mjs')
	
	const compatData = await GetData()
	const aiSourceData = getAISourceData()
	
	console.log('✅ 兼容接口正常工作')
	console.log(`   兼容数据结构完整: ${!!compatData.detail_thinking}`)
	console.log(`   AI源数据格式正确: ${typeof aiSourceData === 'object'}`)

	// 3. 测试依赖模块
	console.log('\n📦 3. 测试依赖模块...')
	
	// 测试escape模块
	const { escapeRegExp, unescapeRegExp, unicodeEscapeToChar, unescapeUnicode } = await import('./scripts/escape.mjs')
	const testString = 'test.regex[]*+?'
	const escaped = escapeRegExp(testString)
	console.log(`✅ escape模块: "${testString}" -> "${escaped}"`)
	
	// 测试prompt_struct模块
	const { margeStructPromptChatLog, structPromptToSingleNoChatLog, buildPromptStruct } = await import('./lib/shells/chat/src/server/prompt_struct.mjs')
	console.log('✅ prompt_struct模块导入成功')

	// 4. 测试proxy生成器
	console.log('\n🔌 4. 测试proxy生成器...')
	const proxyModule = await import('./lib/fount_ai_generators/proxy/main.mjs')
	
	const template = await proxyModule.default.interfaces.AIsource.GetConfigTemplate()
	console.log('✅ proxy生成器配置模板获取成功')
	
	const testConfig = {
		name: 'integration-test',
		url: 'https://api.openai.com/v1/chat/completions',
		model: 'gpt-3.5-turbo',
		apikey: 'test-key',
		model_arguments: { temperature: 0.7, n: 1 },
		convert_config: { passName: false, roleReminding: true }
	}
	
	const aiSource = await proxyModule.default.interfaces.AIsource.GetSource(testConfig, {
		SaveConfig: () => {}
	})
	
	console.log('✅ proxy生成器AI源创建成功')
	console.log(`   AI源类型: ${aiSource.type}`)
	console.log(`   AI源名称: ${aiSource.info[''].name}`)

	// 5. 测试prompt_struct功能
	console.log('\n📝 5. 测试prompt_struct功能...')
	
	const testPromptStruct = {
		char_id: 'gentian',
		Charname: '龙胆',
		UserCharname: '用户',
		ReplyToCharname: '龙胆',
		alternative_charnames: [],
		char_prompt: {
			text: [{ content: '你是龙胆·阿芙萝黛蒂', important: 100 }],
			additional_chat_log: [],
			extension: {}
		},
		user_prompt: {
			text: [{ content: '用户名称：用户', important: 50 }],
			additional_chat_log: [],
			extension: {}
		},
		other_chars_prompt: {},
		world_prompt: { text: [], additional_chat_log: [], extension: {} },
		plugin_prompts: {},
		chat_log: [
			{ name: '用户', role: 'user', content: '你好' },
			{ name: '龙胆', role: 'assistant', content: '你好！我是龙胆·阿芙萝黛蒂，很高兴见到你！' }
		]
	}
	
	const systemPrompt = structPromptToSingleNoChatLog(testPromptStruct)
	const chatLog = margeStructPromptChatLog(testPromptStruct)
	
	console.log('✅ prompt_struct功能正常')
	console.log(`   系统提示词长度: ${systemPrompt.length} 字符`)
	console.log(`   聊天日志条目: ${chatLog.length} 个`)

	// 6. 测试AI源接口
	console.log('\n🤖 6. 测试AI源接口...')
	
	// 测试Call方法结构
	if (typeof aiSource.Call === 'function') {
		console.log('✅ Call方法存在')
	} else {
		console.log('❌ Call方法不存在')
		allTestsPassed = false
	}
	
	// 测试StructCall方法结构
	if (typeof aiSource.StructCall === 'function') {
		console.log('✅ StructCall方法存在')
	} else {
		console.log('❌ StructCall方法不存在')
		allTestsPassed = false
	}
	
	// 测试Tokenizer
	if (aiSource.Tokenizer && typeof aiSource.Tokenizer.get_token_count === 'function') {
		const tokenCount = aiSource.Tokenizer.get_token_count('测试文本')
		console.log(`✅ Tokenizer正常工作，估算token数: ${tokenCount}`)
	} else {
		console.log('❌ Tokenizer不存在或不完整')
		allTestsPassed = false
	}

	// 7. 测试文件结构
	console.log('\n📁 7. 验证文件结构...')
	
	const requiredFiles = [
		'config/config_manager.mjs',
		'config/main.json',
		'config/ai_sources.json',
		'config/calling_order.json',
		'config.mjs',
		'scripts/escape.mjs',
		'lib/shells/chat/src/server/prompt_struct.mjs',
		'lib/fount_ai_generators/proxy/main.mjs'
	]
	
	let missingFiles = 0
	for (const file of requiredFiles) {
		try {
			await Deno.stat(file)
			console.log(`✅ ${file}`)
		} catch {
			console.log(`❌ ${file} - 文件不存在`)
			missingFiles++
			allTestsPassed = false
		}
	}
	
	if (missingFiles === 0) {
		console.log('✅ 所有必需文件都存在')
	} else {
		console.log(`❌ 缺少 ${missingFiles} 个必需文件`)
	}

	// 8. 验证删除的文件
	console.log('\n🗑️ 8. 验证已删除的文件...')
	
	const deletedFiles = [
		'lib/prompt_struct.mjs'
	]
	
	let unexpectedFiles = 0
	for (const file of deletedFiles) {
		try {
			await Deno.stat(file)
			console.log(`❌ ${file} - 文件应该已被删除但仍然存在`)
			unexpectedFiles++
			allTestsPassed = false
		} catch {
			console.log(`✅ ${file} - 已正确删除`)
		}
	}

	// 最终结果
	console.log('\n' + '='.repeat(50))
	if (allTestsPassed) {
		console.log('🎉 所有集成测试通过！')
		console.log('✅ 系统重构成功完成')
		console.log('✅ fount平台兼容性已实现')
		console.log('✅ 配置系统已完全重新设计')
		console.log('✅ proxy生成器正常工作')
		console.log('✅ 所有依赖模块正确适配')
	} else {
		console.log('❌ 部分测试失败')
		console.log('请检查上述错误信息并修复问题')
		Deno.exit(1)
	}

} catch (error) {
	console.error('❌ 集成测试过程中发生错误:', error.message)
	console.error(error.stack)
	allTestsPassed = false
	Deno.exit(1)
}
