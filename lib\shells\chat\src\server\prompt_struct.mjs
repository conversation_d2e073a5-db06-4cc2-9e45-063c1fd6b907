/**
 * 提示词结构处理模块 - 基于fount平台实现
 * 兼容fount平台的prompt_struct接口
 */

/** @typedef {import('../../../../../types/chatLog.mjs').prompt_struct_t} prompt_struct_t */
/** @typedef {import('../../../../../types/charAPI.mjs').charAPI_t} charAPI_t */
/** @typedef {import('../../../../../types/chatLog.mjs').chatLogEntry_t} chatLogEntry_t */
/** @typedef {import('../../../../../types/chatLog.mjs').chatReplyRequest_t} chatReplyRequest_t */

/**
 * 获取单部分提示词的默认结构
 * @returns {Object}
 */
function getSinglePartPrompt() {
	return {
		text: [],
		additional_chat_log: [],
		extension: {},
	}
}

/**
 * 构建提示词结构 (兼容fount平台接口)
 * @param {chatReplyRequest_t} args - 构建参数
 * @param {number} detail_level - 详细级别
 * @returns {Promise<prompt_struct_t>} 提示词结构
 */
export async function buildPromptStruct(args, detail_level = 3) {
	const { char_id, char, user, world, other_chars, plugins, chat_log, UserCharname, ReplyToCharname, Charname } = args
	
	/** @type {prompt_struct_t} */
	const result = {
		char_id,
		UserCharname,
		ReplyToCharname,
		Charname,
		char_prompt: getSinglePartPrompt(),
		user_prompt: getSinglePartPrompt(),
		other_chars_prompt: {},
		world_prompt: getSinglePartPrompt(),
		plugin_prompts: {},
		chat_log,
	}

	// 实现fount平台的detail_level迭代机制
	while (detail_level--) {
		// 处理世界提示词
		if (world) {
			try {
				const worldPrompt = await world.interfaces.chat.GetPrompt(args, result, detail_level)
				if (worldPrompt) {
					result.world_prompt = worldPrompt
				}
			} catch (error) {
				console.warn('Error getting world prompt:', error)
			}
		}
		
		// 处理用户提示词
		if (user) {
			try {
				const userPrompt = await user.interfaces.chat.GetPrompt(args, result, detail_level)
				if (userPrompt) {
					result.user_prompt = userPrompt
				}
			} catch (error) {
				console.warn('Error getting user prompt:', error)
			}
		}
		
		// 处理角色提示词
		if (char) {
			try {
				const charPrompt = await char.interfaces.chat.GetPrompt(args, result, detail_level)
				if (charPrompt) {
					result.char_prompt = charPrompt
				}
			} catch (error) {
				console.warn('Error getting char prompt:', error)
			}
		}
		
		// 处理其他角色提示词
		for (const other_char of Object.keys(other_chars || {})) {
			try {
				const otherCharPrompt = await other_chars[other_char].interfaces.chat?.GetPromptForOther?.(args, result, detail_level)
				if (otherCharPrompt) {
					result.other_chars_prompt[other_char] = otherCharPrompt
				}
			} catch (error) {
				console.warn(`Error getting other char prompt for ${other_char}:`, error)
			}
		}
		
		// 处理插件提示词
		for (const plugin of Object.keys(plugins || {})) {
			try {
				const pluginPrompt = await plugins[plugin].interfaces.chat?.GetPrompt?.(args, result, detail_level)
				if (pluginPrompt) {
					result.plugin_prompts[plugin] = pluginPrompt
				}
			} catch (error) {
				console.warn(`Error getting plugin prompt for ${plugin}:`, error)
			}
		}
	}

	return result
}

/**
 * 将提示词结构转换为单一字符串（不包含聊天日志）
 * @param {prompt_struct_t} prompt - 提示词结构
 * @returns {string} 转换后的字符串
 */
export function structPromptToSingleNoChatLog(prompt) {
	const result = []

	// 角色提示词
	{
		const sorted = prompt.char_prompt.text.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean)
		if (sorted.length > 0) {
			result.push('你需要扮演的角色设定如下：')
			result.push(...sorted)
		}
	}

	// 用户提示词
	{
		const sorted = prompt.user_prompt.text.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean)
		if (sorted.length > 0) {
			result.push('用户的设定如下：')
			result.push(...sorted)
		}
	}

	// 世界提示词
	{
		const sorted = prompt.world_prompt.text.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean)
		if (sorted.length > 0) {
			result.push('当前环境的设定如下：')
			result.push(...sorted)
		}
	}

	// 其他角色提示词
	{
		const sorted = Object.values(prompt.other_chars_prompt).map(char => char.text).filter(Boolean).map(
			char => char.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean)
		).flat().filter(Boolean)
		if (sorted.length > 0) {
			result.push('其他角色的设定如下：')
			result.push(...sorted)
		}
	}

	// 插件提示词
	{
		const sorted = Object.values(prompt.plugin_prompts).map(plugin => plugin?.text).filter(Boolean).map(
			plugin => plugin.sort((a, b) => a.important - b.important).map(text => text.content).filter(Boolean)
		).flat().filter(Boolean)
		if (sorted.length > 0) {
			result.push('你可以使用以下插件，方法如下：')
			result.push(...sorted)
		}
	}

	return result.join('\n')
}

/**
 * 合并提示词结构的聊天日志
 * @param {prompt_struct_t} prompt - 提示词结构
 * @returns {chatLogEntry_t[]} 合并后的聊天日志
 */
export function margeStructPromptChatLog(prompt) {
	const result = [
		...prompt.chat_log,
		...prompt.user_prompt?.additional_chat_log || [],
		...prompt.world_prompt?.additional_chat_log || [],
		...Object.values(prompt.other_chars_prompt).map(char => char.additional_chat_log || []).flat(),
		...Object.values(prompt.plugin_prompts).map(plugin => plugin.additional_chat_log || []).flat(),
		...prompt.char_prompt?.additional_chat_log || [],
	]
	
	/** @type {chatLogEntry_t[]} */
	const flat_result = []
	for (const entry of result) {
		if (entry.logContextBefore) flat_result.push(...entry.logContextBefore)
		flat_result.push(entry)
		if (entry.logContextAfter) flat_result.push(...entry.logContextAfter)
	}
	
	return flat_result.filter(entry => !entry.charVisibility || entry.charVisibility.includes(prompt.char_id))
}

/**
 * 将提示词结构转换为单一字符串（包含聊天日志）
 * @param {prompt_struct_t} prompt - 提示词结构
 * @returns {string} 转换后的字符串
 */
export function structPromptToSingle(prompt) {
	const result = [structPromptToSingleNoChatLog(prompt)]

	result.push('聊天记录如下：')
	margeStructPromptChatLog(prompt).forEach((chatLogEntry) => {
		result.push(chatLogEntry.name + ': ' + chatLogEntry.content)
	})

	return result.join('\n')
}
