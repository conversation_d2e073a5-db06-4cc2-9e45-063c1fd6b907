#!/usr/bin/env deno run --allow-all

/**
 * 测试新的配置管理系统
 */

console.log('🧪 测试新的配置管理系统...')

try {
	// 测试配置管理器
	console.log('\n📦 测试配置管理器导入...')
	const { getConfigManager, initConfig } = await import('./config/config_manager.mjs')
	console.log('✅ 配置管理器导入成功')

	// 初始化配置系统
	console.log('\n🔧 初始化配置系统...')
	const configManager = await initConfig()
	console.log('✅ 配置系统初始化成功')

	// 测试主配置
	console.log('\n📋 测试主配置...')
	const mainConfig = configManager.getMainConfig()
	console.log('✅ 主配置获取成功:')
	console.log(`   用户名: ${mainConfig.username}`)
	console.log(`   平台: ${mainConfig.platform}`)
	console.log(`   语言: ${mainConfig.locale}`)
	console.log(`   调试模式: ${mainConfig.debug}`)

	// 测试AI源配置
	console.log('\n🤖 测试AI源配置...')
	const aiSources = configManager.getAISourcesConfig()
	console.log('✅ AI源配置获取成功:')
	for (const [name, config] of Object.entries(aiSources)) {
		console.log(`   - ${name}: ${config.name} (${config.enabled ? '启用' : '禁用'})`)
	}

	// 测试调用顺序配置
	console.log('\n📞 测试调用顺序配置...')
	const callingOrder = configManager.getCallingOrderConfig()
	console.log('✅ 调用顺序配置获取成功:')
	for (const [type, order] of Object.entries(callingOrder)) {
		console.log(`   - ${type}: [${order.join(', ')}]`)
	}

	// 测试兼容接口
	console.log('\n🔄 测试兼容接口...')
	const { GetData, SetData, getAISourceData } = await import('./config.mjs')
	
	const compatData = await GetData()
	console.log('✅ 兼容接口GetData成功')
	console.log(`   AI源数据: ${JSON.stringify(compatData.AIsources)}`)
	
	const aiSourceData = getAISourceData()
	console.log('✅ 兼容接口getAISourceData成功')
	console.log(`   AI源数据: ${JSON.stringify(aiSourceData)}`)

	// 测试配置更新
	console.log('\n📝 测试配置更新...')
	await configManager.updateMainConfig({ debug: true })
	const updatedConfig = configManager.getMainConfig()
	console.log(`✅ 配置更新成功，调试模式: ${updatedConfig.debug}`)

	// 恢复原始配置
	await configManager.updateMainConfig({ debug: false })
	console.log('✅ 配置已恢复')

	console.log('\n🎉 所有配置系统测试通过！')

} catch (error) {
	console.error('❌ 配置系统测试失败:', error.message)
	console.error(error.stack)
	Deno.exit(1)
}
